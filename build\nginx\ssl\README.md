# SSL证书目录

这个目录用于存放SSL证书文件，用于HTTPS配置。

## 文件说明

将来需要放置以下文件：
- `pxpat.cc.crt` - SSL证书文件
- `pxpat.cc.key` - SSL私钥文件
- `pxpat.cc.pem` - 证书链文件（如果需要）

## 获取SSL证书

可以通过以下方式获取SSL证书：

1. **Let's Encrypt（免费）**
   ```bash
   # 安装certbot
   apt install certbot python3-certbot-nginx
   
   # 获取证书
   certbot --nginx -d testtokenservice.pxpat.cc -d testuserservice.pxpat.cc
   ```

2. **云服务商SSL证书**
   - 阿里云SSL证书
   - 腾讯云SSL证书
   - 其他云服务商

3. **自签名证书（仅用于测试）**
   ```bash
   openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
     -keyout pxpat.cc.key -out pxpat.cc.crt \
     -subj "/C=CN/ST=State/L=City/O=Organization/CN=*.pxpat.cc"
   ```

## 注意事项

- 证书文件权限应设置为 600
- 私钥文件需要妥善保管
- 定期检查证书过期时间
