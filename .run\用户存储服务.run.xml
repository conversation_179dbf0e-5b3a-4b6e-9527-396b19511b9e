<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="用户存储服务" type="GoApplicationRunConfiguration" factoryName="Go Application" folderName="存储组">
    <module name="pxpat-backend" />
    <working_directory value="$PROJECT_DIR$" />
    <kind value="PACKAGE" />
    <package value="pxpat-backend/cmd/storage-cluster/user-storage-service" />
    <directory value="$PROJECT_DIR$" />
    <filePath value="$PROJECT_DIR$/cmd/storage-cluster/user-storage-service/main.go" />
    <method v="2" />
  </configuration>
</component>