<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="点数服务" type="GoApplicationRunConfiguration" factoryName="Go Application" folderName="财政组">
    <module name="pxpat-backend" />
    <working_directory value="$PROJECT_DIR$" />
    <kind value="PACKAGE" />
    <package value="pxpat-backend/cmd/finance-cluster/points-service" />
    <directory value="$PROJECT_DIR$" />
    <filePath value="$PROJECT_DIR$/cmd/finance-cluster/points-service/main.go" />
    <method v="2" />
  </configuration>
</component>