# PostgreSQL管理 - testdbadmin.pxpat.cc
server {
    listen 80;
    server_name testdbadmin.pxpat.cc;
    
    location / {
        proxy_pass http://adminer:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
}

# Redis管理 - testredisadmin.pxpat.cc
server {
    listen 80;
    server_name testredisadmin.pxpat.cc;
    
    location / {
        proxy_pass http://redis-commander:8081;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
}

# RabbitMQ管理 - testmqadmin.pxpat.cc
server {
    listen 80;
    server_name testmqadmin.pxpat.cc;
    
    location / {
        proxy_pass http://rabbitmq:15672;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
}

# Consul界面 - testconsul.pxpat.cc
server {
    listen 80;
    server_name testconsul.pxpat.cc;
    
    location / {
        proxy_pass http://consul:8500;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
}
